"""
ElevenLabs transcript provider implementation.
"""

import asyncio
import hashlib
import json
import os
from pathlib import Path
from typing import Dict, Optional, Union

from elevenlabs import ElevenLabs
from pydub import AudioSegment

from acva_ai._params import (
    ELEVENLABS_API_KEY,
    ELEVENLABS_MAX_AUDIO_SIZE,
    ELEVENLABS_MODEL_ID,
)
from acva_ai.llm.llm_cache import LLM_AUDIO_CACHE_DIR
from acva_ai.utils.general_utils import calculate_cost
from acva_ai.utils.usage import LLMUsage, ResponseUsage

os.makedirs(LLM_AUDIO_CACHE_DIR, exist_ok=True)


class AudioTranscriptionError(Exception):
    """Custom exception for audio transcription errors."""

    pass


class AudioFileError(Exception):
    """Custom exception for audio file related errors."""

    pass


def _get_file_size_mb(file_path: str) -> float:
    """Get file size in megabytes."""
    try:
        return os.path.getsize(file_path) / (1024 * 1024)
    except OSError as e:
        raise AudioFileError(f"Error getting file size: {e}")


def _validate_file_size(file_path: str, max_size_mb: float) -> None:
    """
    Validate that the file is within the size limit.

    Args:
        file_path: Path to the audio file
        max_size_mb: Maximum allowed size in megabytes

    Raises:
        AudioFileError: If the file is too large
    """
    try:
        size_mb = _get_file_size_mb(file_path)
        if size_mb > max_size_mb:
            raise AudioFileError(
                f"Audio file too large: {size_mb:.2f} MB exceeds limit of {max_size_mb} MB"
            )
        print(f"File size: {size_mb:.2f} MB (within {max_size_mb} MB limit)")
    except Exception as e:
        if isinstance(e, AudioFileError):
            raise
        raise AudioFileError(f"Error validating file size: {e}")


def _validate_convert_audio_file(audio_file_path: str) -> str:
    """
    Validate audio file exists and convert it to PCM_S16LE_16 format.

    For pcm_s16le_16, the input audio must be:
    - 16-bit PCM at a 16kHz sample rate
    - Single channel (mono)
    - Little-endian byte order

    Args:
        audio_file_path: Path to the original audio file

    Returns:
        str: Path to the converted audio file in the required format

    Raises:
        AudioFileError: If there are issues with the audio file or conversion
    """
    try:
        audio_path = Path(audio_file_path)
        if not audio_path.exists():
            raise AudioFileError(f"Audio file not found: {audio_file_path}")

        if not audio_path.is_file():
            raise AudioFileError(f"Path is not a file: {audio_file_path}")

        # Check if file is readable
        try:
            with open(audio_path, "rb") as f:
                if not f.read(1):  # Try to read at least 1 byte
                    raise AudioFileError(f"Audio file is empty: {audio_file_path}")
        except PermissionError:
            raise AudioFileError(f"Permission denied reading file: {audio_file_path}")
        except OSError as e:
            raise AudioFileError(f"Error reading audio file: {e}")

        # Load audio file using pydub
        try:
            audio = AudioSegment.from_file(audio_file_path)
        except Exception as e:
            raise AudioFileError(f"Could not load audio file {audio_file_path}: {e}")

        # Convert to required PCM_S16LE_16 format
        try:
            # Convert to mono (single channel)
            audio = audio.set_channels(1)

            # Set sample rate to 16kHz
            audio = audio.set_frame_rate(16000)

            # Set sample width to 16-bit (2 bytes)
            audio = audio.set_sample_width(2)

            # Generate output path for converted file
            original_path = Path(audio_file_path)
            converted_filename = f"{original_path.stem}_pcm_s16le_16.wav"
            converted_path = original_path.parent / converted_filename

            # Export with PCM S16LE codec (16-bit PCM, little-endian)
            audio.export(str(converted_path), format="wav", codec="pcm_s16le")

            print(f"Audio converted to PCM_S16LE_16 format: {converted_path}")
            return str(converted_path)

        except Exception as e:
            raise AudioFileError(f"Error converting audio to PCM_S16LE_16 format: {e}")

    except Exception as e:
        if isinstance(e, (AudioFileError, AudioTranscriptionError)):
            raise
        raise AudioFileError(f"Unexpected error validating/converting audio file: {e}")


def _get_audio_duration_from_file(audio_file_path: str) -> float:
    """Get audio duration in seconds from file path."""
    try:
        audio = AudioSegment.from_file(audio_file_path)
        return len(audio) / 1000  # pydub duration is in milliseconds
    except Exception as e:
        raise AudioFileError(f"Error getting duration from file: {e}")


def _load_cached_response(cache_filepath: str) -> Optional[Dict]:
    """Load cached response if available."""
    try:
        if os.path.isfile(cache_filepath):
            with open(cache_filepath, "r", encoding="utf-8") as f:
                cached_response = json.loads(f.read())
                return cached_response
    except (json.JSONDecodeError, IOError, OSError) as e:
        print(f"Error reading cache file: {e}")
    return None


def _save_to_cache(cache_filepath: str, response_data: Dict) -> None:
    """Save response to cache."""
    try:
        with open(cache_filepath, "w", encoding="utf-8") as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)
    except (IOError, OSError) as e:
        print(f"Error writing to cache file: {e}")


async def _calculate_usage_cost_from_file(
    audio_file_path: str,
    response_data: Dict,
    response_usage: ResponseUsage,
) -> None:
    """Calculate and track usage costs for file-based transcription."""
    try:
        audio_duration_seconds = _get_audio_duration_from_file(audio_file_path)
        cost = await calculate_cost(
            model=ELEVENLABS_MODEL_ID, audio_duration_seconds=audio_duration_seconds
        )

        llm_usage = LLMUsage(
            model_id=ELEVENLABS_MODEL_ID,
            cost=cost,
            input_tokens=0,  # Not applicable for audio
            output_tokens=len(
                response_data.get("text", "")
            ),  # Use text length as proxy
            audio_input_duration=audio_duration_seconds,
        )

        response_usage.add_llm_usage(llm_usage)
    except Exception as e:
        print(f"Error calculating transcription cost: {e}")


class ElevenLabsTranscriptProvider:
    """
    ElevenLabs transcript provider implementation.

    This provider uses the ElevenLabs Speech-to-Text API for audio transcription.
    It supports PCM_S16LE_16 format conversion and provides detailed word-level data.
    """

    def __init__(self):
        """Initialize the ElevenLabs transcript provider."""
        self.provider_name = "elevenlabs"
        self.model_id = ELEVENLABS_MODEL_ID

    def get_max_file_size_mb(self) -> float:
        """Get the maximum file size supported by ElevenLabs."""
        return ELEVENLABS_MAX_AUDIO_SIZE

    def get_provider_name(self) -> str:
        """Get the provider name."""
        return self.provider_name

    async def transcribe_audio_file(
        self,
        audio_file_path: str,
        language: Optional[str] = "ron",
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from a file path using ElevenLabs.

        Args:
            audio_file_path: Path to the audio file
            language: Optional language code
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            **kwargs: Additional ElevenLabs-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the audio file
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            result = await self.call_audio_transcription_from_file_async(
                audio_file_path=audio_file_path,
                language=language,
                use_cache=use_cache,
                response_usage=response_usage,
                max_file_size_mb=max_file_size_mb or self.get_max_file_size_mb(),
                **kwargs,
            )
            return result.get("text", "")
        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"ElevenLabs transcription error: {e}")

    async def transcribe_audio_segment(
        self,
        audio_segment: AudioSegment,
        language: Optional[str] = "ron",
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        **kwargs,
    ) -> str:
        """
        Transcribe audio from an AudioSegment using ElevenLabs.

        Note: ElevenLabs provider doesn't have a direct AudioSegment method,
        so we'll save the segment to a temporary file and use the file method.

        Args:
            audio_segment: AudioSegment object to transcribe
            language: Optional language code
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            **kwargs: Additional ElevenLabs-specific parameters

        Returns:
            Transcribed text as string

        Raises:
            AudioFileError: If there are issues with the AudioSegment
            AudioTranscriptionError: If there are issues with the transcription API
        """
        import tempfile

        try:
            # Create a temporary file for the audio segment
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # Export the audio segment to the temporary file
                audio_segment.export(temp_path, format="wav")

                # Use the file transcription method
                result = await self.transcribe_audio_file(
                    audio_file_path=temp_path,
                    language=language,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    max_file_size_mb=max_file_size_mb,
                    **kwargs,
                )
                return result

            finally:
                # Clean up the temporary file
                try:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                except Exception as cleanup_error:
                    print(
                        f"Warning: Could not cleanup temp file {temp_path}: {cleanup_error}"
                    )

        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(
                f"ElevenLabs AudioSegment transcription error: {e}"
            )

    async def call_transcription_async(
        self,
        audio_input: Union[str, AudioSegment],
        language: Optional[str] = "ron",
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: Optional[float] = None,
        **kwargs,
    ) -> Dict:
        """
        Call ElevenLabs transcription API and return full response data.

        Args:
            audio_input: Either file path (str) or AudioSegment object
            language: Optional language code
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes
            **kwargs: Additional ElevenLabs-specific parameters

        Returns:
            Dict containing full transcription response data

        Raises:
            AudioFileError: If there are issues with the audio input
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            if isinstance(audio_input, str):
                # File path input
                result = await self.call_audio_transcription_from_file_async(
                    audio_file_path=audio_input,
                    language=language,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    max_file_size_mb=max_file_size_mb or self.get_max_file_size_mb(),
                    **kwargs,
                )
            else:
                # AudioSegment input - convert to file first
                import tempfile

                with tempfile.NamedTemporaryFile(
                    suffix=".wav", delete=False
                ) as temp_file:
                    temp_path = temp_file.name

                try:
                    audio_input.export(temp_path, format="wav")
                    result = await self.call_audio_transcription_from_file_async(
                        audio_file_path=temp_path,
                        language=language,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        max_file_size_mb=max_file_size_mb
                        or self.get_max_file_size_mb(),
                        **kwargs,
                    )
                finally:
                    try:
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    except Exception as cleanup_error:
                        print(
                            f"Warning: Could not cleanup temp file {temp_path}: {cleanup_error}"
                        )

            # Standardize the response format
            return self._standardize_response(result)

        except Exception as e:
            if isinstance(e, (AudioFileError, AudioTranscriptionError)):
                raise
            raise AudioTranscriptionError(f"ElevenLabs API call error: {e}")

    async def call_audio_transcription_from_file_async(
        self,
        audio_file_path: str,
        language: Optional[str] = "ron",
        temperature: float = 0,
        use_cache: bool = True,
        response_usage: Optional[ResponseUsage] = None,
        max_file_size_mb: float = ELEVENLABS_MAX_AUDIO_SIZE,
        cleanup_converted_file: bool = True,
    ) -> Dict:
        """
        Asynchronous call to ElevenLabs Audio Transcription from file path with caching.

        The audio file will be automatically converted to PCM_S16LE_16 format (16-bit PCM,
        16kHz sample rate, mono, little-endian) before transcription.

        Args:
            audio_file_path: Path to the audio file
            language: Optional language code
            temperature: Sampling temperature (0 to 2)
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            max_file_size_mb: Maximum allowed file size in megabytes (default: 1000.0)
            cleanup_converted_file: Whether to delete the converted file after processing (default: True)

        Returns:
            Dict containing transcription results

        Raises:
            AudioFileError: If there are issues with the audio file or if file is too large
            AudioTranscriptionError: If there are issues with the transcription API
        """
        try:
            # Validate file size before processing
            _validate_file_size(audio_file_path, max_file_size_mb)

            # Validate audio file exists and convert to PCM_S16LE_16 format
            converted_audio_path = _validate_convert_audio_file(audio_file_path)

            # Generate cache key based on converted file content
            with open(converted_audio_path, "rb") as f:
                file_content = f.read()
            content_hash = hashlib.md5(file_content).hexdigest()

            # Setup cache
            cache_key = f"{content_hash}_{self.model_id}_{language or 'auto'}"
            cache_filename = f"{cache_key}.json"
            cache_filepath = os.path.join(LLM_AUDIO_CACHE_DIR, cache_filename)

            # Try to load from cache
            if use_cache:
                cached_response = _load_cached_response(cache_filepath)
                if cached_response is not None:
                    return cached_response

            # Initialize ElevenLabs client
            if not ELEVENLABS_API_KEY:
                raise AudioTranscriptionError(
                    "ELEVENLABS_API_KEY environment variable is not set"
                )

            client = ElevenLabs(api_key=ELEVENLABS_API_KEY)

            # Make transcription request using ElevenLabs library
            try:
                # Run the synchronous client method in a thread pool
                import time

                start = time.time()

                # Prepare API call parameters
                api_params = {
                    "model_id": self.model_id,
                    "file": open(converted_audio_path, "rb"),
                    "temperature": temperature,
                }

                # Only include language_code if it's not None (for auto-detection)
                if language is not None:
                    api_params["language_code"] = language

                response = await asyncio.to_thread(
                    client.speech_to_text.convert, **api_params
                )
                print(f"Response time: {time.time() - start:.2f} seconds")

                # Convert response to dict format
                response_data = {
                    "text": response.text,
                    "language_code": response.language_code,
                    "language_probability": response.language_probability,
                    "words": [
                        {
                            "text": word.text,
                            "type": word.type,
                            "start": word.start,
                            "end": word.end,
                            "speaker_id": getattr(word, "speaker_id", None),
                            "logprob": getattr(word, "logprob", None),
                        }
                        for word in (response.words or [])
                    ],
                }

            except Exception as e:
                raise AudioTranscriptionError(f"ElevenLabs API error: {e}")

            # Calculate usage if requested
            if response_usage is not None:
                await _calculate_usage_cost_from_file(
                    converted_audio_path, response_data, response_usage
                )

            # Cache the response
            if use_cache:
                _save_to_cache(cache_filepath, response_data)

            # Cleanup converted file if requested
            if cleanup_converted_file:
                try:
                    if os.path.exists(converted_audio_path):
                        os.remove(converted_audio_path)
                        print(
                            f"Cleaned up converted audio file: {converted_audio_path}"
                        )
                except Exception as cleanup_error:
                    print(
                        f"Warning: Could not cleanup converted file {converted_audio_path}: {cleanup_error}"
                    )

            return response_data

        except (AudioFileError, AudioTranscriptionError):
            raise
        except Exception as e:
            raise AudioTranscriptionError(
                f"Unexpected error in call_audio_transcription_from_file_async: {e}"
            )

    def _standardize_response(self, response_data: Dict) -> Dict:
        """
        Standardize response format across providers.

        Args:
            response_data: Raw response data from ElevenLabs

        Returns:
            Standardized response dictionary
        """
        # Ensure all responses have at least these fields
        standardized = {
            "text": response_data.get("text", ""),
            "language_code": response_data.get("language_code"),
            "provider": "elevenlabs",
        }

        # Preserve original response data
        standardized["raw_response"] = response_data

        return standardized

    async def _generate_transcription(
        self,
        audio_segment: AudioSegment,
        language: str = "ron",
        response_usage: Optional[ResponseUsage] = None,
    ) -> tuple[str, dict[str, str]]:
        """
        ElevenLabs-specific transcription generation.

        Args:
            audio_segment: AudioSegment to transcribe
            language: Language code for transcription
            response_usage: Optional ResponseUsage object to track costs

        Returns:
            Tuple of (transcribed_text, chunks_status_dict)
        """
        result_transcriptions = []
        chunks_status = {}
        current_timestamp = 0.0

        results = await self.transcribe_audio_segment(
            audio_segment=audio_segment,
            language=language,
            response_usage=response_usage,
        )

        for result in results:
            if len(result):
                chunks_status[current_timestamp] = "Transcript generated successfully"
                result_transcriptions.append(result)
            else:
                chunks_status[current_timestamp] = "Error in generating transcript"

            current_timestamp += len(audio_segment) / 1000  # Convert ms to seconds

        result_transcript = " ".join(result_transcriptions)
        timestamps = list(chunks_status.keys()) + [current_timestamp]

        chunks_status_dict = {
            f"{int(start)}-{int(end)}": status
            for start, end, status in zip(
                timestamps[:-1], timestamps[1:], chunks_status.values()
            )
        }

        return result_transcript, chunks_status_dict

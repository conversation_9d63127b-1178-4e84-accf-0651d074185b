import asyncio
import logging
import traceback
from typing import Op<PERSON>, <PERSON><PERSON>


from acva_ai.database import mongo_instance
from acva_ai.llm.transcript_orchestrator import (
    TranscriptOrchestrator,
    TranscriptProvider,
)
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage

# Set up logging
logger = logging.getLogger(__name__)

from typing import Dict

from pydub import AudioSegment


async def _generate_transcription(
    audio_segment: AudioSegment,
    transcript_provider: TranscriptProvider,
    langauge: str = "ro",
    response_usage: Optional[ResponseUsage] = None,
) -> Tuple[str, Dict[str, str]]:

    if transcript_provider is None:
        raise ValueError("Transcript provider must be specified")

    # Create transcript orchestrator
    orchestrator = TranscriptOrchestrator(
        primary_provider=transcript_provider,
    )

    # Use provider-specific transcription generation
    result_transcript, chunks_status_dict = await orchestrator.generate_transcription(
        audio_segment=audio_segment,
        language=langauge,
        response_usage=response_usage,
    )

    return result_transcript, chunks_status_dict


async def generate_transcription(
    task_id: str,
    audio_segment: AudioSegment,
    processing_status: Optional[ProcessingStatus],
    visit_report: Optional[VisitReport],
    transcript_provider: TranscriptProvider,
    langauge: str = "ro",
    response_usage: Optional[ResponseUsage] = None,
):
    if transcript_provider is None:
        raise ValueError("Transcript provider must be specified")

    """
    Processes an AudioSegment and generates the trasncription
    """
    logger.info(f"[Task {task_id}] Starting transcription generation")
    processing_status.start_stage("transcription")
    mongo_instance.update_processing_status(task_id, processing_status.dict())

    try:
        result_transcript, chunks_status_dict = await _generate_transcription(
            audio_segment=audio_segment,
            langauge=langauge,
            response_usage=response_usage,
            transcript_provider=transcript_provider,
        )
        visit_report.raw_transcript = result_transcript
        visit_report.transcript_observations = [
            f"{k}: {v}" for k, v in chunks_status_dict.items()
        ]
        processing_status.complete_stage("transcription")
        logger.info(f"[Task {task_id}] Completed transcription successfully")

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("transcription", e, stack_trace)
        processing_status.finalize()
        logger.error(f"[Task {task_id}] Error generating transcript {e}\n{stack_trace}")

    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())


def test():
    sample_audio_path = ".data/demo/1e4cd7fa-b4a1-47ce-99aa-abdf66885b2b.wav"
    audio_segment = AudioSegment.from_file(sample_audio_path)

    # Process the audio segment
    result_transcript, status_dict = asyncio.run(
        _generate_transcription(audio_segment=audio_segment)
    )
    print(result_transcript)
    print(status_dict)


if __name__ == "__main__":
    test()
